/* BrettenWood Projects - Custom Styles */

:root {
    --primary-black: #000000;
    --primary-white: #ffffff;
    --light-gray: #f8f9fa;
    --dark-gray: #343a40;
    --accent-gray: #6c757d;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--primary-black);
    background-color: var(--primary-white);
}

/* Navigation Styles */
.navbar {
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-white) !important;
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--primary-white);
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Main Content */
.main-content {
    margin-top: 76px;
    min-height: calc(100vh - 76px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--dark-gray) 100%);
    color: var(--primary-white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    animation: fadeInUp 1s ease 0.4s both;
}

/* Buttons */
.btn-custom {
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-outline-light {
    border: 2px solid var(--primary-white);
    color: var(--primary-white);
}

.btn-outline-light:hover {
    background-color: var(--primary-white);
    color: var(--primary-black);
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.btn-dark {
    background-color: var(--primary-black);
    border: 2px solid var(--primary-black);
    color: var(--primary-white);
}

.btn-dark:hover {
    background-color: transparent;
    color: var(--primary-black);
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Section Styles */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-black);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--accent-gray);
    margin-bottom: 3rem;
}

/* Flash Messages */
.flash-messages {
    position: fixed;
    top: 76px;
    left: 0;
    right: 0;
    z-index: 1050;
    padding: 0 15px;
}

.alert {
    border-radius: 10px;
    border: none;
    box-shadow: var(--shadow);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-padding {
        padding: 60px 0;
    }
    
    .btn-custom {
        padding: 10px 25px;
        font-size: 0.9rem;
    }
}
