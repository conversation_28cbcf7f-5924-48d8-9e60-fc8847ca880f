from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import json
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'brettenwood_secret_key_2025'

# Ensure data directory exists
if not os.path.exists('data'):
    os.makedirs('data')

# Initialize reviews file if it doesn't exist
reviews_file = 'data/reviews.json'
if not os.path.exists(reviews_file):
    with open(reviews_file, 'w') as f:
        json.dump([], f)

def load_reviews():
    """Load reviews from JSON file"""
    try:
        with open(reviews_file, 'r') as f:
            return json.load(f)
    except:
        return []

def save_review(review_data):
    """Save a new review to JSON file"""
    reviews = load_reviews()
    review_data['id'] = len(reviews) + 1
    review_data['date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    reviews.append(review_data)
    with open(reviews_file, 'w') as f:
        json.dump(reviews, f, indent=2)

# Tank systems data
TANK_SYSTEMS = {
    '1000L': {
        'name': '1000L Jojo Tank System',
        'capacity': '1000 Litres',
        'pump': 'Grundfos SB 3-45 A Submersible Pump',
        'pump_specs': '0.75kW, 45L/min flow rate',
        'includes': ['1000L Jojo Tank', 'Grundfos Pump', 'Pressure Switch', 'Non-return Valve', 'Tank Stand', 'Installation Kit'],
        'price': 'R 8,500',
        'image': 'jojo-1000.png'
    },
    '2500L': {
        'name': '2500L Jojo Tank System',
        'capacity': '2500 Litres',
        'pump': 'Grundfos SB 3-45 A Submersible Pump',
        'pump_specs': '1.1kW, 60L/min flow rate',
        'includes': ['2500L Jojo Tank', 'Grundfos Pump', 'Pressure Switch', 'Non-return Valve', 'Tank Stand', 'Installation Kit', 'Level Indicator'],
        'price': 'R 15,500',
        'image': 'jojo-2500.webp'
    },
    '5000L': {
        'name': '5000L Jojo Tank System',
        'capacity': '5000 Litres',
        'pump': 'Grundfos SB 5-45 A Submersible Pump',
        'pump_specs': '1.5kW, 75L/min flow rate',
        'includes': ['5000L Jojo Tank', 'Grundfos Pump', 'Pressure Switch', 'Non-return Valve', 'Tank Stand', 'Installation Kit', 'Level Indicator', 'Overflow Kit'],
        'price': 'R 28,500',
        'image': 'jojo-5200.webp'
    }
}

@app.route('/')
def home():
    """Home page with about us section"""
    return render_template('home.html')

@app.route('/systems')
def systems():
    """Tank systems page"""
    return render_template('systems.html', tank_systems=TANK_SYSTEMS)

@app.route('/portfolio')
def portfolio():
    """Portfolio page showing previous jobs"""
    return render_template('portfolio.html')

@app.route('/reviews')
def reviews():
    """Reviews page"""
    reviews_data = load_reviews()
    return render_template('reviews.html', reviews=reviews_data)

@app.route('/submit_review', methods=['POST'])
def submit_review():
    """Handle review submission"""
    try:
        review_data = {
            'name': request.form.get('name'),
            'email': request.form.get('email'),
            'rating': int(request.form.get('rating')),
            'title': request.form.get('title'),
            'review': request.form.get('review')
        }
        
        # Basic validation
        if not all([review_data['name'], review_data['rating'], review_data['review']]):
            flash('Please fill in all required fields.', 'error')
            return redirect(url_for('reviews'))
        
        save_review(review_data)
        flash('Thank you for your review! It has been submitted successfully.', 'success')
        return redirect(url_for('reviews'))
    
    except Exception as e:
        flash('An error occurred while submitting your review. Please try again.', 'error')
        return redirect(url_for('reviews'))

@app.route('/contact')
def contact():
    """Contact page"""
    return render_template('contact.html')

@app.route('/submit_contact', methods=['POST'])
def submit_contact():
    """Handle contact form submission"""
    try:
        # In a real application, you would send an email or save to database
        flash('Thank you for your message! We will get back to you soon.', 'success')
        return redirect(url_for('contact'))
    except Exception as e:
        flash('An error occurred while sending your message. Please try again.', 'error')
        return redirect(url_for('contact'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
